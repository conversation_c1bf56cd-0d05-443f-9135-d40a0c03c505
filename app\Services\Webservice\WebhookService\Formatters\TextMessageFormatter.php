<?php

namespace App\Services\Webservice\WebhookService\Formatters;

use App\Contracts\Webservice\WebhookFormatter;

class TextMessageFormatter implements WebhookFormatter
{
    public function format(array $message): array
    {
        return [
            'sender' => $message['sender']['id'],
            'receiver' => $message['recipient']['id'],
            'message_type' => 'text',
            'text' => $message['message']['text'],
            'timestamp' => gmdate('Y-m-d\TH:i:s\Z', (int)$message['timestamp'] / 1000),
            'is_admin' => isset($message['message']['is_echo']),
        ];
    }
}
