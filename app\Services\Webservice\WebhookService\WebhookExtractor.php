<?php

namespace App\Services\Webservice\WebhookService;

use App\Services\Webservice\WebhookService\Formatters\ImageMessageFormatter;
use App\Services\Webservice\WebhookService\Formatters\PostbackMessageFormatter;
use App\Services\Webservice\WebhookService\Formatters\PostShareFormatter;
use App\Services\Webservice\WebhookService\Formatters\QuickReplyMessageFormatter;
use App\Services\Webservice\WebhookService\Formatters\ReelsMessageFormatter;
use App\Services\Webservice\WebhookService\Formatters\StoryReplyFormatter;
use App\Services\Webservice\WebhookService\Formatters\TextMessageFormatter;
use App\Services\Webservice\WebhookService\Formatters\AudioMessageFormatter;
use App\Services\Webservice\WebhookService\Formatters\VideoMessageFormatter;
use App\Services\Webservice\WebhookService\Formatters\ReadReceiptFormatter;

class WebhookExtractor
{
    protected StoryReplyFormatter $storyReplyFormatter;
    protected TextMessageFormatter $textFormatter;
    protected ImageMessageFormatter $imageFormatter;
    protected AudioMessageFormatter $audioFormatter;
    protected VideoMessageFormatter $videoFormatter;
    protected ReelsMessageFormatter $reelsMessageFormatter;
    protected PostShareFormatter $postShareFormatter;
	protected PostbackMessageFormatter $postbackFormatter;
	protected QuickReplyMessageFormatter $quickReplyFormatter;
    protected ReadReceiptFormatter $readReceiptFormatter;

    public function __construct(
        StoryReplyFormatter $storyReplyFormatter,
        TextMessageFormatter $textFormatter,
        ImageMessageFormatter $imageFormatter,
        AudioMessageFormatter $audioFormatter,
        VideoMessageFormatter $videoFormatter,
        ReelsMessageFormatter $reelsMessageFormatter,
        PostShareFormatter $postShareFormatter,
        PostbackMessageFormatter $postbackFormatter,
        QuickReplyMessageFormatter $quickReplyFormatter,
        ReadReceiptFormatter $readReceiptFormatter,
    ) {
        $this->storyReplyFormatter = $storyReplyFormatter;
        $this->textFormatter = $textFormatter;
        $this->imageFormatter = $imageFormatter;
        $this->audioFormatter = $audioFormatter;
        $this->videoFormatter = $videoFormatter;
        $this->reelsMessageFormatter = $reelsMessageFormatter;
        $this->postShareFormatter = $postShareFormatter;
	    $this->postbackFormatter = $postbackFormatter;
		$this->quickReplyFormatter = $quickReplyFormatter;
        $this->readReceiptFormatter = $readReceiptFormatter;
    }

    public function extract(array $webhook): ?array
    {
        if (!isset($webhook['entry'][0]['messaging'])) return null;

        $message = $webhook['entry'][0]['messaging'][0];

	    if (isset($message['message']['quick_reply'])) {
		    return $this->quickReplyFormatter->format($message);
	    }

        if (isset($message['message']['reply_to']['story'])) {
            return $this->storyReplyFormatter->format($message);
        }

        if (isset($message['message']['text'])) {
            return $this->textFormatter->format($message);
        }

        if (isset($message['message']['attachments'][0]['type'])) {
            $type = $message['message']['attachments'][0]['type'];

            if ($type === 'image') {
                return $this->imageFormatter->format($message);
            }

            if ($type === 'video') {
                return $this->videoFormatter->format($message);
            }

            if ($type === 'audio') {
                return $this->audioFormatter->format($message);
            }

            if ($type === 'ig_reel') {
                return $this->reelsMessageFormatter->format($message);
            }

            if ($type === 'share') {
                return $this->postShareFormatter->format($message);
            }
        }

        if (isset($message['read'])) {
            return $this->readReceiptFormatter->format($message);
        }

	    if (isset($message['postback'])) {
		    return $this->postbackFormatter->format($message);
	    }

        throw new \InvalidArgumentException("Unknown message type" . json_encode($message));
    }
}
