<?php

namespace App\Services\Actions\ManyMessageAgents;

use App\DTOs\UpdateOrCreateManyMessageAgentDTO;
use App\Models\ManyMessageAgent;

class UpdateOrCreateManyMessageAgent
{
    public function handle(UpdateOrCreateManyMessageAgentDTO $dto): ManyMessageAgent
    {
        $attributes = ['email' => $dto->email];
        $values = [];

        if ($dto->instaId !== null) {
            $values['insta_id'] = $dto->instaId;
        }

        $agent = ManyMessageAgent::updateOrCreate($attributes, $values);

        return $agent;
    }
}
